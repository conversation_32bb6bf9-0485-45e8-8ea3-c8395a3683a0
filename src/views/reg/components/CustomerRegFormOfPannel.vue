<template>
  <a-spin :spinning="confirmLoading">
    <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <!-- 紧凑型头部信息展示 -->
      <div class="customer-header-info">
        <!-- 核心状态信息条 -->
        <div class="status-bar">
          <div class="avatar-section">
            <div class="avatar-container">
              <a-image
                :width="60"
                :height="80"
                :src="formData.customerAvatar ? getFileAccessHttpUrl(formData.customerAvatar) : defaultAvatar"
                :fallback="defaultAvatar"
                class="customer-avatar"
                alt="客户头像"
                :preview="{
                  mask: false,
                }"
              />
              <a-button size="small" type="text" @click="takePhone" class="photo-btn">
                <Icon icon="ant-design:camera-outlined" />
              </a-button>
            </div>
          </div>

          <div class="status-tags">
            <div class="status-row">
              <div class="status-group">
                <span class="status-label">体检类型:</span>
                <a-tag color="blue" class="status-tag">{{ formData.examCategory || '未设置' }}</a-tag>
              </div>

              <div class="status-group">
                <span class="status-label">体检号:</span>
                <a-tag color="purple" class="status-tag">{{ formData.examNo || '待生成' }}</a-tag>
              </div>

              <div class="status-group">
                <span class="status-label">支付:</span>
                <a-tag :color="formData.paymentState == '已支付' ? 'success' : 'error'" class="status-tag">
                  {{ formData.paymentState || '待支付' }}
                </a-tag>
              </div>

              <div class="status-group">
                <span class="status-label">登记:</span>
                <a-tag :color="formData.status == '已登记' ? 'success' : 'orange'" class="status-tag">
                  {{ formData.status || '未登记' }}
                </a-tag>
              </div>

              <div class="status-group">
                <span class="status-label">检查:</span>
                <a-tag :color="formData.checkState == '已检查' ? 'success' : 'default'" class="status-tag">
                  {{ formData.checkState || '未检查' }}
                </a-tag>
                <a-tag
                  v-if="formData.retrieveStatus"
                  :color="formData.retrieveStatus == '1' ? 'success' : 'error'"
                  size="small"
                  class="status-tag-small"
                >
                  {{ formData.retrieveStatus == '1' ? '已交表' : '未交表' }}
                </a-tag>
              </div>

              <div class="status-group">
                <span class="status-label">总检:</span>
                <a-tag :color="formData.summaryStatus == '审核通过' ? 'success' : 'default'" class="status-tag">
                  {{ formData.summaryStatus || '未总检' }}
                </a-tag>
              </div>
            </div>
          </div>

          <!-- 详情按钮集成在状态栏右侧 -->
          <div class="detail-toggle-inline">
            <a @click="showDetailInfo = !showDetailInfo" class="detail-toggle-btn">
              <Icon :icon="showDetailInfo ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
            </a>
          </div>
        </div>

        <!-- 详细信息区域 -->
        <CollapseTransition>
          <div v-show="showDetailInfo" class="detail-info">
            <a-descriptions size="small" :column="2" bordered>
              <a-descriptions-item label="预约时间">
                {{ formData.bookingDate || '未预约' }}
              </a-descriptions-item>
              <a-descriptions-item label="预约员工">
                {{ formData.creatorBy_dictText || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="登记时间">
                {{ formData.regTime || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="所属单位" :span="2">
                {{ formData.companyName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">
                {{ formData.createTime || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="所属预约" :span="3">
                {{ formData.companyRegName ? formData.companyRegName + (formData.teamName || '') : '未预约' }}
                <a v-if="formData.companyRegName" @click="showCompanyTeam" class="detail-link">详情</a>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </CollapseTransition>
      </div>

      <div class="row-title">
        <a-typography-title :level="5">档案及体检信息</a-typography-title>
        <a-space>
          <a-button type="dashed" size="small" danger @click="toggleSearchStatus = !toggleSearchStatus">
            {{ toggleSearchStatus ? '收起' : '展开全部' }}
            <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
          </a-button>
          <!-- 字段配置按钮 - 管理员可见 -->
          <a-button v-if="hasConfigPermission" type="dashed" size="small" @click="openFieldConfig">
            <Icon icon="ant-design:setting-outlined" />
            配置字段
          </a-button>
          <a-button size="middle" type="primary" @click="submitForm"><SaveOutlined />保存</a-button>
        </a-space>
      </div>
      <!-- 动态渲染外部区域字段 - 简化逻辑：不在collapseFields中的字段都在外部显示 -->
      <a-row>
        <template v-for="field in outsideFields" :key="field.fieldKey">
          <a-col :span="getFieldComponent(field.fieldKey).span || 12">
            <!-- 体检分类 -->
            <a-form-item v-if="field.fieldKey === 'examCategory'" :label="field.fieldName" v-bind="validateInfos.examCategory">
              <j-dict-select-tag
                v-model:value="formData.examCategory"
                :useDicColor="true"
                dictCode="examination_type"
                placeholder="请选择体检分类"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 预约日期 -->
            <a-form-item v-else-if="field.fieldKey === 'appointmentDate'" :label="field.fieldName" v-bind="validateInfos.appointmentDate">
              <a-date-picker
                placeholder="请选择预约日期"
                v-model:value="formData.appointmentDate"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 姓名 -->
            <a-form-item v-else-if="field.fieldKey === 'name'" :label="field.fieldName" v-bind="validateInfos.name">
              <a-input v-model:value="formData.name" placeholder="请输入姓名" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 证件类型 -->
            <a-form-item v-else-if="field.fieldKey === 'cardType'" :label="field.fieldName" v-bind="validateInfos.cardType">
              <j-dict-select-tag
                v-model:value="formData.cardType"
                dictCode="idcard_type"
                placeholder="请选择证件类型"
                :disabled="disabled"
                :useDicColor="true"
                size="middle"
              />
            </a-form-item>

            <!-- 证件号 -->
            <a-form-item v-else-if="field.fieldKey === 'idCard'" :label="field.fieldName" v-bind="validateInfos.idCard">
              <a-input
                v-model:value="formData.idCard"
                :placeholder="idcDataSource.action == 'addReg' ? idcData.msg : '请输入证件号'"
                :disabled="disabled"
                size="middle"
                @change="handleIdCard"
              />
            </a-form-item>

            <!-- 性别 -->
            <a-form-item v-else-if="field.fieldKey === 'gender'" :label="field.fieldName" v-bind="validateInfos.gender">
              <j-dict-select-tag
                v-model:value="formData.gender"
                :useDicColor="false"
                dictCode="sex"
                placeholder="请选择性别"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 年龄 -->
            <a-form-item v-else-if="field.fieldKey === 'age'" :label="field.fieldName" v-bind="validateInfos.age">
              <a-input placeholder="请输入年龄" v-model:value="formData.age" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 出生日期 -->
            <a-form-item v-else-if="field.fieldKey === 'birthday'" :label="field.fieldName" v-bind="validateInfos.birthday">
              <a-date-picker
                placeholder="请选择出生日期"
                v-model:value="formData.birthday"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 电话 -->
            <a-form-item v-else-if="field.fieldKey === 'phone'" :label="field.fieldName" v-bind="validateInfos.phone">
              <a-input v-model:value="formData.phone" placeholder="请输入电话" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 职业 -->
            <a-form-item v-else-if="field.fieldKey === 'career'" :label="field.fieldName" v-bind="validateInfos.career">
              <j-async-search-select
                :async="true"
                v-model:value="formData.career"
                placeholder="请选择职业"
                dict="dict_career,name,id"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 民族 -->
            <a-form-item v-else-if="field.fieldKey === 'nation'" :label="field.fieldName" v-bind="validateInfos.nation">
              <a-input v-model:value="formData.nation" placeholder="请输入民族" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 血型 -->
            <a-form-item v-else-if="field.fieldKey === 'bloodType'" :label="field.fieldName" v-bind="validateInfos.bloodType">
              <j-dict-select-tag
                :useDicColor="true"
                v-model:value="formData.bloodType"
                dictCode="blood_type"
                placeholder="请选择血型"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 国籍 -->
            <a-form-item v-else-if="field.fieldKey === 'countryCode'" :label="field.fieldName" v-bind="validateInfos.countryCode">
              <j-async-search-select
                v-model:value="formData.countryCode"
                :dict="'country'"
                placeholder="请选择国籍"
                :disabled="disabled"
                @change="handleCountryChange"
                size="middle"
              />
            </a-form-item>

            <!-- 邮政编码 -->
            <a-form-item v-else-if="field.fieldKey === 'postCode'" :label="field.fieldName" v-bind="validateInfos.postCode">
              <a-input v-model:value="formData.postCode" placeholder="请输入邮政编码" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 文化程度 -->
            <a-form-item v-else-if="field.fieldKey === 'eduLevel'" :label="field.fieldName" v-bind="validateInfos.eduLevel">
              <j-dict-select-tag
                v-model:value="formData.eduLevel"
                dictCode="edu_level"
                placeholder="请选择文化程度"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 省市区县 -->
            <a-form-item v-else-if="field.fieldKey === 'pcaCode'" :label="field.fieldName" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
              <div v-if="formData.addressDetail && !showAddrComp" style="display: flex; align-items: center">
                <span>{{ formData.addressDetail }}</span>
                <a @click="showAddrComp = true" style="margin-left: 10px">更换</a>
              </div>
              <pcasv @change="handleArea" :level="4" v-else />
            </a-form-item>

            <!-- 详细地址 -->
            <a-form-item v-else-if="field.fieldKey === 'address'" :label="field.fieldName" v-bind="validateInfos.address">
              <a-textarea v-model:value="formData.address" placeholder="请输入详细地址" :disabled="disabled" :rows="2" />
            </a-form-item>

            <!-- 紧急联系人 -->
            <a-form-item v-else-if="field.fieldKey === 'emergencyContact'" :label="field.fieldName" v-bind="validateInfos.emergencyContact">
              <a-input v-model:value="formData.emergencyContact" placeholder="请输入紧急联系人" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 紧急电话 -->
            <a-form-item v-else-if="field.fieldKey === 'emergencyPhone'" :label="field.fieldName" v-bind="validateInfos.emergencyPhone">
              <a-input v-model:value="formData.emergencyPhone" placeholder="请输入紧急电话" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 健康证号 -->
            <a-form-item v-else-if="field.fieldKey === 'healthNo'" :label="field.fieldName" v-bind="validateInfos.healthNo">
              <a-input v-model:value="formData.healthNo" placeholder="请输入健康证号" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 单位名称 -->
            <a-form-item v-else-if="field.fieldKey === 'companyName'" :label="field.fieldName" v-bind="validateInfos.companyName">
              <a-input v-model:value="formData.companyName" placeholder="请输入单位名称" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 所属单位 -->
            <a-form-item v-else-if="field.fieldKey === 'belongCompany'" :label="field.fieldName" v-bind="validateInfos.belongCompany">
              <a-input v-model:value="formData.belongCompany" placeholder="请输入所属单位" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 所属部门 -->
            <a-form-item v-else-if="field.fieldKey === 'department'" :label="field.fieldName" v-bind="validateInfos.department">
              <a-input v-model:value="formData.department" placeholder="请输入所属部门" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 婚姻状况 -->
            <a-form-item v-else-if="field.fieldKey === 'marriageStatus'" :label="field.fieldName" v-bind="validateInfos.marriageStatus">
              <j-dict-select-tag
                v-model:value="formData.marriageStatus"
                dictCode="marriage_status"
                placeholder="请选择婚姻状况"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 客户类别 -->
            <a-form-item v-else-if="field.fieldKey === 'customerCategory'" :label="field.fieldName" v-bind="validateInfos.customerCategory">
              <j-dict-select-tag
                v-model:value="formData.customerCategory"
                dictCode="customer_category"
                placeholder="请选择客户类别"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 是否备孕 -->
            <a-form-item v-else-if="field.fieldKey === 'isPregnancyPrep'" :label="field.fieldName" v-bind="validateInfos.pregnancyFlag">
              <j-switch v-model:value="formData.pregnancyFlag" :options="[1, 0]" :disabled="disabled" />
            </a-form-item>

            <!-- 体检卡号 -->
            <a-form-item v-else-if="field.fieldKey === 'examCardNo'" :label="field.fieldName" v-bind="validateInfos.examCardNo">
              <a-input v-model:value="formData.examCardNo" placeholder="请输入体检卡号" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 工龄 -->
            <a-form-item v-else-if="field.fieldKey === 'workYears'" :label="field.fieldName" v-bind="validateInfos.workYears">
              <a-input-number v-model:value="formData.workYears" placeholder="请输入工龄" :disabled="disabled" size="middle" style="width: 100%" />
            </a-form-item>

            <!-- 补检 -->
            <a-form-item v-else-if="field.fieldKey === 'supplyFlag'" :label="field.fieldName" v-bind="validateInfos.supplyFlag">
              <j-switch v-model:value="formData.supplyFlag" :options="[1, 0]" :disabled="disabled" />
            </a-form-item>

            <!-- 预缴 -->
            <a-form-item v-else-if="field.fieldKey === 'prePayFlag'" :label="field.fieldName" v-bind="validateInfos.prePayFlag">
              <j-switch v-model:value="formData.prePayFlag" :options="[1, 0]" :disabled="disabled" />
            </a-form-item>

            <!-- 是否复查 -->
            <a-form-item v-else-if="field.fieldKey === 'reExamStatus'" :label="field.fieldName" v-bind="validateInfos.reExamStatus">
              <j-switch v-model:value="formData.reExamStatus" :options="[1, 0]" :disabled="disabled" />
            </a-form-item>

            <!-- 复查备注 -->
            <a-form-item v-else-if="field.fieldKey === 'reExamRemark'" :label="field.fieldName" v-bind="validateInfos.reExamRemark">
              <a-input v-model:value="formData.reExamRemark" placeholder="请输入复查备注" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 发票抬头 -->
            <a-form-item v-else-if="field.fieldKey === 'recipeTitle'" :label="field.fieldName" v-bind="validateInfos.recipeTitle">
              <a-input v-model:value="formData.recipeTitle" placeholder="请输入发票抬头" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 原检证件号 -->
            <a-form-item v-else-if="field.fieldKey === 'originalIdCard'" :label="field.fieldName" v-bind="validateInfos.originCustomerIdcard">
              <a-input v-model:value="formData.originCustomerIdcard" placeholder="请输入原检证件号" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 与原检关系 -->
            <a-form-item v-else-if="field.fieldKey === 'relationWithOriginal'" :label="field.fieldName" v-bind="validateInfos.originCustomerRelation">
              <j-dict-select-tag
                v-model:value="formData.originCustomerRelation"
                dictCode="origin_relation"
                placeholder="请选择与原检关系"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 介绍人 -->
            <a-form-item v-else-if="field.fieldKey === 'introducer'" :label="field.fieldName" v-bind="validateInfos.introducer">
              <a-input v-model:value="formData.introducer" placeholder="请输入介绍人" :disabled="disabled" size="middle" />
            </a-form-item>

            <!-- 保密等级 -->
            <a-form-item v-else-if="field.fieldKey === 'secretLevel'" :label="field.fieldName" v-bind="validateInfos.secretLevel">
              <j-dict-select-tag
                v-model:value="formData.secretLevel"
                dictCode="secret_level"
                placeholder="请选择保密等级"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>

            <!-- 备注 -->
            <a-form-item v-else-if="field.fieldKey === 'remark'" :label="field.fieldName" v-bind="validateInfos.remark">
              <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :disabled="disabled" :rows="3" />
            </a-form-item>
          </a-col>
        </template>
      </a-row>

      <!-- 保留原有的固定字段作为后备（当配置为空时） -->
      <a-row v-if="outsideFields.length === 0">
        <a-col :span="12">
          <a-form-item label="体检分类" v-bind="validateInfos.examCategory">
            <j-dict-select-tag
              v-model:value="formData.examCategory"
              :useDicColor="true"
              dictCode="examination_type"
              placeholder="请选择体检分类"
              :disabled="disabled"
              size="middle"
            />
          </a-form-item>
        </a-col>

        <!-- 特殊字段：婚姻状况 - 根据配置决定是否在外部区域显示 -->
        <a-col :span="12" v-if="shouldShowInOutside('marriageStatus')">
          <a-form-item label="婚姻状况" v-bind="validateInfos.marriageStatus">
            <j-dict-select-tag
              :useDicColor="true"
              v-model:value="formData.marriageStatus"
              dictCode="material_type"
              placeholder="请选择婚姻状况"
              :disabled="disabled"
              size="middle"
            />
          </a-form-item>
        </a-col>
        <template v-if="hasPermission('reg:selectCompanyTeam')">
          <a-col :lg="24">
            <a-form-item
              name="companyRegId"
              label="所属预约"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              v-bind="validateInfos.companyRegId"
            >
              <j-async-search-select
                size="middle"
                placeholder="所属预约"
                @change="handleCompanyRegChange"
                v-model:value="formData.companyRegId"
                :api="searchCompanyReg"
                :field-mapping="{
                  value: 'id',
                  text: 'regName',
                }"
              />
            </a-form-item>
          </a-col>
          <a-col :lg="24">
            <a-form-item name="teamId" label="所属分组" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" v-bind="validateInfos.teamId">
              <a-select size="middle" placeholder="所属分组" v-model:value="formData.teamId" :allow-clear="true">
                <a-select-option :value="item.id" v-for="item in teamList">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </template>

        <!-- 省市区县 - 根据配置决定是否显示 -->
        <a-col :span="24" v-if="shouldShowInOutside('pcaCode')">
          <a-form-item label="省市区县" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
            <div v-if="formData.addressDetail && !showAddrComp" style="display: flex; align-items: center"
              ><span>{{ formData.addressDetail }}</span>
              <a @click="showAddrComp = true" style="margin-left: 10px">更换</a>
            </div>
            <pcasv @change="handleArea" :level="4" v-else />
          </a-form-item>
        </a-col>
        <!-- 详细地址 - 根据配置决定是否显示 -->
        <a-col :span="12" v-if="shouldShowInOutside('address')">
          <a-form-item label="详细地址" v-bind="validateInfos.address">
            <a-textarea v-model:value="formData.address" placeholder="请输入详细地址" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <!-- 紧急联系人 - 根据配置决定是否显示 -->
        <a-col :span="12" v-if="shouldShowInOutside('emergencyContact')">
          <a-form-item label="紧急联系人" v-bind="validateInfos.emergencyContact">
            <a-input v-model:value="formData.emergencyContact" placeholder="请输入紧急联系人" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
        <!-- 紧急电话 - 根据配置决定是否显示 -->
        <a-col :span="12" v-if="shouldShowInOutside('emergencyPhone')">
          <a-form-item label="紧急电话" v-bind="validateInfos.emergencyPhone">
            <a-input v-model:value="formData.emergencyPhone" placeholder="请输入紧急电话" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>

        <!-- 固定字段：是否备孕 -->
        <a-col :span="12">
          <a-form-item label="是否备孕" v-bind="validateInfos.pregnancyFlag">
            <j-switch v-model:value="formData.pregnancyFlag" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <!-- 固定字段：单位名称 -->
        <a-col :span="12">
          <a-form-item label="所属单位">
            <CompanyAutoComplete
              v-model:value="formData.companyName"
              placeholder="请选择单位"
              :disabled="disabled"
              @select="handleCompanySelect"
              @change="handleCompanyAutoCompleteChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item name="companyDeptId" label="所属部门" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
            <JTreeSelect
              v-model:value="formData.companyDeptId"
              placeholder="请选择部门"
              :disabled="disabled || !formData.companyId"
              :dict="`company_dept,name,id`"
              :condition="`pid='${formData.companyId || ''}' and del_flag='0'`"
              :reload="companyDeptReloadKey"
              pid-field="pid"
              :pid-value="formData.companyId || ''"
              @change="handleDeptChange"
            />
          </a-form-item>
        </a-col>

        <!-- 客户类别 - 根据配置决定是否显示 -->
        <a-col :span="12" v-if="shouldShowInOutside('customerCategory')">
          <a-form-item label="客户类别" v-bind="validateInfos.customerCategory">
            <j-dict-select-tag
              :useDicColor="true"
              v-model:value="formData.customerCategory"
              dictCode="customer_type"
              placeholder="请选择客户类别"
              :disabled="disabled"
              size="middle"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="原检证件号" v-bind="validateInfos.originCustomerIdcard" :extra="formData.originCustomer ?? ''">
            <a-input-search
              v-model:value="formData.originCustomerIdcard"
              placeholder="请输入原体检人身份证号"
              @search="handleOriginCustomerSearch"
              :loading="originCustomerSearchLoading"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="与原检关系" v-bind="validateInfos.originCustomer">
            <j-dict-select-tag
              v-model:value="formData.originCustomerRelation"
              dictCode="origin_relation"
              placeholder="请选择与原检关系"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" v-bind="validateInfos.remark">
            <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :disabled="disabled" size="middle" />
          </a-form-item>
        </a-col>
      </a-row>

      <div v-show="toggleSearchStatus">
        <!-- 基于配置动态渲染折叠区域字段 -->
        <a-row v-if="collapseFields.length > 0">
          <template v-for="field in collapseFields" :key="field.fieldKey">
            <a-col :span="getFieldComponent(field.fieldKey).span || 12">
              <!-- 体检分类 -->
              <a-form-item v-if="field.fieldKey === 'examCategory'" :label="field.fieldName" v-bind="validateInfos.examCategory">
                <j-dict-select-tag
                  v-model:value="formData.examCategory"
                  :useDicColor="true"
                  dictCode="examination_type"
                  placeholder="请选择体检分类"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 预约日期 -->
              <a-form-item v-else-if="field.fieldKey === 'appointmentDate'" :label="field.fieldName" v-bind="validateInfos.appointmentDate">
                <a-date-picker
                  placeholder="请选择预约日期"
                  v-model:value="formData.appointmentDate"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 姓名 -->
              <a-form-item v-else-if="field.fieldKey === 'name'" :label="field.fieldName" v-bind="validateInfos.name">
                <a-input v-model:value="formData.name" placeholder="请输入姓名" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 证件类型 -->
              <a-form-item v-else-if="field.fieldKey === 'cardType'" :label="field.fieldName" v-bind="validateInfos.cardType">
                <j-dict-select-tag
                  v-model:value="formData.cardType"
                  dictCode="idcard_type"
                  placeholder="请选择证件类型"
                  :disabled="disabled"
                  :useDicColor="true"
                  size="middle"
                />
              </a-form-item>

              <!-- 证件号 -->
              <a-form-item v-else-if="field.fieldKey === 'idCard'" :label="field.fieldName" v-bind="validateInfos.idCard">
                <a-input v-model:value="formData.idCard" placeholder="请输入证件号" :disabled="disabled" size="middle" @change="handleIdCard" />
              </a-form-item>

              <!-- 性别 -->
              <a-form-item v-else-if="field.fieldKey === 'gender'" :label="field.fieldName" v-bind="validateInfos.gender">
                <j-dict-select-tag
                  v-model:value="formData.gender"
                  :useDicColor="true"
                  dictCode="sex"
                  placeholder="请选择性别"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 年龄 -->
              <a-form-item v-else-if="field.fieldKey === 'age'" :label="field.fieldName" v-bind="validateInfos.age">
                <a-input placeholder="请输入年龄" v-model:value="formData.age" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 出生日期 -->
              <a-form-item v-else-if="field.fieldKey === 'birthday'" :label="field.fieldName" v-bind="validateInfos.birthday">
                <a-date-picker
                  placeholder="请选择出生日期"
                  v-model:value="formData.birthday"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 电话 -->
              <a-form-item v-else-if="field.fieldKey === 'phone'" :label="field.fieldName" v-bind="validateInfos.phone">
                <a-input v-model:value="formData.phone" placeholder="请输入电话" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 职业 -->
              <a-form-item v-else-if="field.fieldKey === 'career'" :label="field.fieldName" v-bind="validateInfos.career">
                <j-async-search-select
                  :async="true"
                  v-model:value="formData.career"
                  placeholder="请选择职业"
                  dict="dict_career,name,id"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 民族 -->
              <a-form-item v-else-if="field.fieldKey === 'nation'" :label="field.fieldName" v-bind="validateInfos.nation">
                <a-input v-model:value="formData.nation" placeholder="请输入民族" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 血型 -->
              <a-form-item v-else-if="field.fieldKey === 'bloodType'" :label="field.fieldName" v-bind="validateInfos.bloodType">
                <j-dict-select-tag
                  :useDicColor="true"
                  v-model:value="formData.bloodType"
                  dictCode="blood_type"
                  placeholder="请选择血型"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 国籍 -->
              <a-form-item v-else-if="field.fieldKey === 'countryCode'" :label="field.fieldName" v-bind="validateInfos.countryCode">
                <j-async-search-select
                  v-model:value="formData.countryCode"
                  :dict="'country'"
                  placeholder="请选择国籍"
                  :disabled="disabled"
                  @change="handleCountryChange"
                  size="middle"
                />
              </a-form-item>

              <!-- 邮政编码 -->
              <a-form-item v-else-if="field.fieldKey === 'postCode'" :label="field.fieldName" v-bind="validateInfos.postCode">
                <a-input v-model:value="formData.postCode" placeholder="请输入邮政编码" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 文化程度 -->
              <a-form-item v-else-if="field.fieldKey === 'eduLevel'" :label="field.fieldName" v-bind="validateInfos.eduLevel">
                <j-dict-select-tag
                  v-model:value="formData.eduLevel"
                  dictCode="edu_level"
                  placeholder="请选择文化程度"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 婚姻状况 -->
              <a-form-item v-else-if="field.fieldKey === 'marriageStatus'" :label="field.fieldName" v-bind="validateInfos.marriageStatus">
                <j-dict-select-tag
                  v-model:value="formData.marriageStatus"
                  dictCode="marriage_status"
                  placeholder="请选择婚姻状况"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 客户类别 -->
              <a-form-item v-else-if="field.fieldKey === 'customerCategory'" :label="field.fieldName" v-bind="validateInfos.customerCategory">
                <j-dict-select-tag
                  v-model:value="formData.customerCategory"
                  dictCode="customer_category"
                  placeholder="请选择客户类别"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 省市区县 -->
              <a-form-item v-else-if="field.fieldKey === 'pcaCode'" :label="field.fieldName" v-bind="validateInfos.pcaCode" :span="24">
                <div v-if="formData.addressDetail && !showAddrComp" style="display: flex; align-items: center">
                  <span>{{ formData.addressDetail }}</span>
                  <a @click="showAddrComp = true" style="margin-left: 10px">更换</a>
                </div>
                <pcasv @change="handleArea" :level="4" v-else />
              </a-form-item>

              <!-- 详细地址 -->
              <a-form-item v-else-if="field.fieldKey === 'address'" :label="field.fieldName" v-bind="validateInfos.address" :span="24">
                <a-textarea v-model:value="formData.address" placeholder="请输入详细地址" :disabled="disabled" :rows="2" />
              </a-form-item>

              <!-- 紧急联系人 -->
              <a-form-item v-else-if="field.fieldKey === 'emergencyContact'" :label="field.fieldName" v-bind="validateInfos.emergencyContact">
                <a-input v-model:value="formData.emergencyContact" placeholder="请输入紧急联系人" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 紧急电话 -->
              <a-form-item v-else-if="field.fieldKey === 'emergencyPhone'" :label="field.fieldName" v-bind="validateInfos.emergencyPhone">
                <a-input v-model:value="formData.emergencyPhone" placeholder="请输入紧急电话" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 是否备孕 -->
              <a-form-item v-else-if="field.fieldKey === 'isPregnancyPrep'" :label="field.fieldName" v-bind="validateInfos.pregnancyFlag">
                <j-switch v-model:value="formData.pregnancyFlag" :options="[1, 0]" :disabled="disabled" />
              </a-form-item>

              <!-- 健康证号 -->
              <a-form-item v-else-if="field.fieldKey === 'healthNo'" :label="field.fieldName" v-bind="validateInfos.healthNo">
                <a-input v-model:value="formData.healthNo" placeholder="请输入健康证号" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 体检卡号 -->
              <a-form-item v-else-if="field.fieldKey === 'examCardNo'" :label="field.fieldName" v-bind="validateInfos.examCardNo">
                <a-input v-model:value="formData.examCardNo" placeholder="请输入体检卡号" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 工龄 -->
              <a-form-item v-else-if="field.fieldKey === 'workYears'" :label="field.fieldName" v-bind="validateInfos.workYears">
                <a-input-number v-model:value="formData.workYears" placeholder="请输入工龄" :disabled="disabled" size="middle" style="width: 100%" />
              </a-form-item>

              <!-- 单位名称 -->
              <a-form-item v-else-if="field.fieldKey === 'companyName'" :label="field.fieldName" v-bind="validateInfos.companyName">
                <CompanyAutoComplete
                  v-model:value="formData.companyName"
                  placeholder="请选择单位"
                  :disabled="disabled"
                  @select="handleCompanySelect"
                  @change="handleCompanyAutoCompleteChange"
                />
                <!--                <a-input v-model:value="formData.companyName" placeholder="请输入单位名称" :disabled="disabled" size="middle" />-->
              </a-form-item>

              <!-- 所属单位 -->
              <a-form-item v-else-if="field.fieldKey === 'belongCompany'" :label="field.fieldName" v-bind="validateInfos.belongCompany">
                <a-input v-model:value="formData.belongCompany" placeholder="请输入所属单位" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 所属部门 -->
              <a-form-item v-else-if="field.fieldKey === 'department'" :label="field.fieldName" v-bind="validateInfos.department">
                <a-input v-model:value="formData.department" placeholder="请输入所属部门" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 补检 -->
              <a-form-item v-else-if="field.fieldKey === 'supplyFlag'" :label="field.fieldName" v-bind="validateInfos.supplyFlag">
                <j-switch v-model:value="formData.supplyFlag" :options="[1, 0]" :disabled="disabled" />
              </a-form-item>

              <!-- 预缴 -->
              <a-form-item v-else-if="field.fieldKey === 'prePayFlag'" :label="field.fieldName" v-bind="validateInfos.prePayFlag">
                <j-switch v-model:value="formData.prePayFlag" :options="[1, 0]" :disabled="disabled" />
              </a-form-item>

              <!-- 是否复查 -->
              <a-form-item v-else-if="field.fieldKey === 'reExamStatus'" label="是否复查" v-bind="validateInfos.reExamStatus">
                <j-switch v-model:value="formData.reExamStatus" :options="[1, 0]" :disabled="disabled" />
              </a-form-item>

              <!-- 复查备注 -->
              <a-form-item v-else-if="field.fieldKey === 'reExamRemark'" label="复查备注" v-bind="validateInfos.reExamRemark">
                <a-input v-model:value="formData.reExamRemark" placeholder="请输入复查备注" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 发票抬头 -->
              <a-form-item v-else-if="field.fieldKey === 'recipeTitle'" label="发票抬头" v-bind="validateInfos.recipeTitle">
                <a-input v-model:value="formData.recipeTitle" placeholder="请输入发票抬头" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 原检证件号 -->
              <a-form-item v-else-if="field.fieldKey === 'originalIdCard'" label="原检证件号" v-bind="validateInfos.originCustomerIdcard">
                <a-input v-model:value="formData.originCustomerIdcard" placeholder="请输入原检证件号" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 与原检关系 -->
              <a-form-item v-else-if="field.fieldKey === 'relationWithOriginal'" label="与原检关系" v-bind="validateInfos.originCustomerRelation">
                <j-dict-select-tag
                  v-model:value="formData.originCustomerRelation"
                  dictCode="origin_relation"
                  placeholder="请选择与原检关系"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 介绍人 -->
              <a-form-item v-else-if="field.fieldKey === 'introducer'" label="介绍人" v-bind="validateInfos.introducer">
                <a-input v-model:value="formData.introducer" placeholder="请输入介绍人" :disabled="disabled" size="middle" />
              </a-form-item>

              <!-- 保密等级 -->
              <a-form-item v-else-if="field.fieldKey === 'secretLevel'" label="保密等级" v-bind="validateInfos.secretLevel">
                <j-dict-select-tag
                  v-model:value="formData.secretLevel"
                  dictCode="secret_level"
                  placeholder="请选择保密等级"
                  :disabled="disabled"
                  size="middle"
                />
              </a-form-item>

              <!-- 备注 -->
              <a-form-item v-else-if="field.fieldKey === 'remark'" label="备注" v-bind="validateInfos.remark" :span="24">
                <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :disabled="disabled" :rows="3" />
              </a-form-item>
            </a-col>
          </template>
        </a-row>

        <!-- 原有静态字段（作为后备） -->
        <a-row v-else>
          <a-col :span="12">
            <a-form-item label="民族" v-bind="validateInfos.nation">
              <a-input v-model:value="formData.nation" placeholder="请输入民族" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="血型" v-bind="validateInfos.bloodType">
              <j-dict-select-tag
                :useDicColor="true"
                v-model:value="formData.bloodType"
                dictCode="blood_type"
                placeholder="请选择血型"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>
          </a-col>

          <!--          <a-col :span="12">
            <a-form-item label="国籍" v-bind="validateInfos.country">
              <j-dict-select-tag
                :useDicColor="true"
                v-model:value="formData.country"
                dictCode="country"
                placeholder="请选择国籍"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>
          </a-col>-->
          <a-col :span="12">
            <a-form-item label="国籍">
              <j-async-search-select
                :async="true"
                v-model:value="formData.countryCode"
                @select="handleCountryChange"
                placeholder="请选择国籍"
                dict="dict_country,name,code"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="邮政编码" v-bind="validateInfos.postCode">
              <a-input v-model:value="formData.postCode" placeholder="请输入邮政编码" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="文化程度" v-bind="validateInfos.eduLevel">
              <j-dict-select-tag
                :useDicColor="true"
                v-model:value="formData.eduLevel"
                dictCode="edu_level"
                placeholder="请选择文化程度"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="健康证号" v-bind="validateInfos.healthNo">
              <a-input v-model:value="formData.healthNo" placeholder="请输入健康证号" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="体检卡号" v-bind="validateInfos.examCardNo">
              <a-input v-model:value="formData.examCardNo" placeholder="请输入体检卡号" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="介绍人" v-bind="validateInfos.introducer">
              <a-input v-model:value="formData.introducer" placeholder="请输入介绍人" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="补检" v-bind="validateInfos.supplyFlag">
              <j-switch v-model:value="formData.supplyFlag" :options="[1, 0]" :disabled="disabled" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预缴" v-bind="validateInfos.prePayFlag">
              <j-switch v-model:value="formData.prePayFlag" :options="[1, 0]" :disabled="disabled" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="是否复查" v-bind="validateInfos.reExamStatus">
              <j-switch v-model:value="formData.reExamStatus" :options="[1, 0]" :disabled="disabled" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="复查备注" v-bind="validateInfos.reExamRemark">
              <a-input v-model:value="formData.reExamRemark" placeholder="请输入复查备注" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发票抬头" v-bind="validateInfos.recipeTitle">
              <a-input v-model:value="formData.recipeTitle" placeholder="请输入发票抬头" :disabled="disabled" size="middle" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="保密等级" v-bind="validateInfos.secretLevel">
              <j-dict-select-tag
                :useDicColor="true"
                v-model:value="formData.secretLevel"
                dictCode="secret_level"
                placeholder="请选择保密等级"
                :disabled="disabled"
                size="middle"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-spin>
  <camera-modal
    ref="cameraModal"
    @on-photo-taken="handleCameraOk"
    :constraints="{ video: { width: { ideal: 720 }, height: { ideal: 1280 } }, facingMode: 'environment' }"
  />
  <company-team-detail-modal ref="companyTeamDetailModal" />
  <!--  <CustomerRegTeamDetailModal
    ref="customerTeamDetailModal"
    @share="handleShareTeamLimitAmount(true)"
    @cancel-share="handleShareTeamLimitAmount(false)"
  /> -->
  <TeamLimitAmount4ShareDetailModal
    ref="teamLimitAmount4ShareDetailModal"
    @share="handleShareTeamLimitAmount(true)"
    @cancel-share="handleShareTeamLimitAmount(false)"
  />

  <!-- 字段配置弹窗 -->
  <FormFieldConfigModal ref="configModalRef" @success="handleConfigSuccess" />
</template>

<script lang="ts" setup>
  import { computed, defineExpose, defineProps, inject, nextTick, reactive, Ref, ref, toRaw, watch, watchEffect } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import JTreeSelect from '/@/components/Form/src/jeecg/components/JTreeSelect.vue';
  import CompanyTeamDetailModal from '/@/views/reg/components/CompanyTeamDetailModal.vue';
  import { getValueType } from '/@/utils';
  import {
    changeItemGroupByCompanyTeam,
    getItemGroupOfTeam,
    getRiskFactorsByWorkType,
    getTeamAndLimitInfoByIdCard,
    regOne,
    saveOrUpdate,
  } from '../CustomerReg.api';
  import { Form, message, theme } from 'ant-design-vue';
  import CameraModal from '/src/components/Camera/CameraModal.vue';
  import { uploadFile } from '@/utils/upload';
  import defaultAvatar from '/@/assets/images/defaultAvatar.png';
  import IDCardUtil from '@/utils/IDCardUtil';
  import { JAsyncSearchSelect } from '@/components/Form';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import { companyTeamList, getCompanyDeptListByPid, getCompanyRegById, searchCompanyReg } from '@/views/reg/CompanyReg.api';
  import dayjs from 'dayjs';
  import { IdcData } from '#/utils';
  import { queryCustomerByIdCard } from '@/views/reg/Customer.api';
  import { getRootCompanyById } from '@/views/basicinfo/Company.api';
  import TeamLimitAmount4ShareDetailModal from '@/views/reg/components/TeamLimitAmount4ShareDetailModal.vue';
  import Pcasv from '@/components/pcca/Pcasv.vue';
  import { SaveOutlined } from '@ant-design/icons-vue';
  import { Icon } from '@/components/Icon';
  import { CollapseTransition } from '@/components/Transition';
  import { usePermission } from '@/hooks/web/usePermission';
  import { setFormData2Null } from '@/utils/common/formUtils';
  import FormFieldConfigModal from './FormFieldConfigModal.vue';
  import {
    FieldDisplayConfig,
    FieldDisplayLocation,
    FORM_TYPES,
    getActiveFormDisplayConfig,
    getDefaultFieldConfig,
    migrateFieldConfig,
    testBackwardCompatibility,
    validateFieldConfig,
  } from '../FormFieldConfig.api';

  import CompanyAutoComplete from '@/components/basicinfo/CompanyAutoComplete.vue';
  import type { CompanyAutoCompleteDTO } from '@/types/basicinfo/company';
  import { getCompanyAutoComplete } from '@/api/basicinfo/company';

  const { createConfirm, createErrorModal } = useMessage();
  const { token } = theme.useToken();
  const { hasPermission } = usePermission();

  /**接收并监听身份证信息，据此进行查询*/
  const idcData = inject<IdcData>('idCardDataKey', {
    data: {},
    ok: false,
    msg: '',
    state: '',
    action: '',
  });
  const { notification } = useMessage();
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: {
      type: Object,
      default: () => {},
    },
  });

  const toggleSearchStatus = ref<boolean>(false);
  const showDetailInfo = ref<boolean>(false);
  const fieldDisplayConfig = ref<FieldDisplayConfig[]>([]);
  const configModalRef = ref();
  const isCalculatingRiskTime = ref<boolean>(false); // 标志变量，防止循环计算
  const quickRiskInput = ref<string>(''); // 快速输入接害工龄
  const IDCardUtils = new IDCardUtil();
  const companyTeamDetailModal = ref(null);
  const customerTeamDetailModal = ref(null);
  const teamLimitAmount4ShareDetailModal = ref(null);
  const cameraModal: Ref<InstanceType<typeof CameraModal> | null> = ref(null);
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok', 'regOk', 'changeItemOk', 'changeItemCancel']);
  const originCompanyTeamId = ref<any>(null);
  const formData = reactive<Record<string, any>>({
    id: '',
    examNo: '',
    customerAvatar: '',
    customerId: '',
    archivesNum: '',
    customerRegNum: '',
    hisInpatientId: '',
    name: '',
    gender: '',
    age: null,
    ageUnit: '',
    birthday: null,
    phone: '',
    marriageStatus: null,
    pregnancyFlag: null,
    nation: '',
    bloodType: '',
    cardType: '居民身份证',
    idCard: '',
    examCardNo: '',
    medicalCardNo: '',
    healthNo: '',
    postCode: '',
    email: '',
    customerCategory: '',
    examCategory: '健康体检',
    teamId: null,
    teamName: null,
    teamNum: null,
    bookingDate: null,
    companyDeptId: null,
    companyDeptName: null,
    country: '中国',
    countryCode: '156',
    industry: null,
    workNo: null,
    career: null,
    jobStatus: null,
    workType: null,
    workShop: '1',
    riskFactor: null,
    workYears: null,
    workMonths: null,
    riskYears: null,
    riskMonths: null,
    reExamStatus: null,
    reExamRemark: null,
    monitoringType: null,
    occIrradiation: null,
    recipeTitle: null,
    eduLevel: null,
    introducer: null,
    remark: null,
    companyRegId: null,
    companyRegName: null,
    checkDate: null,
    creatorBy: null,
    creator: null,
    checkState: null,
    infoSource: null,
    paymentState: null,
    operId: null,
    operName: null,
    operTime: null,
    auditDate: null,
    originCustomer: null,
    supplyFlag: null,
    prePayFlag: null,
    webQueryCode: '',
    companyName: null,
    secretLevel: null,
    companyId: null,
    createTime: null,
    regTime: null,
    guidancePrintTimes: null,
    serialNo: null,
    retrieveStatus: null,
    retrieveTime: null,
    retrieveBy: null,
    retrieveImg: null,
    companyTeam: null,
    companyReg: null,
    suitName: null,
    summaryStatus: null,
    totalPrice: null,
    payedAmount: null,
    remainAmount: null,
    emergencyContact: null,
    emergencyPhone: null,
    regDate: null,
    genderDesc: null,
    personCompanyName: null,
    breakfirstFlag: null,
    preSummaryStatus: null,
    summaryTime: null,
    preSummaryTime: null,
    summaryAuditTime: null,
    reportPrintTime: null,
    reportEditLockFlag: null,
    reportEditLockBy: null,
    reportEditLocker: null,
    reportEditLockTime: null,
    status: null,
    creatorBy_dictText: null,
    pca: '',
    province: '',
    city: '',
    area: '',
    provinceCode: '',
    cityCode: '',
    areaCode: '',
    originCustomerIdcard: null,
    originCustomerRelation: null,
    address: '',
    addressDetail: '',
    riskStartTime: null,
    limitAmount: null,
    appointmentDate: dayjs().format('YYYY-MM-DD'),
    originCustomerLimitAmountId: '',
    riskProtect: null,
    uploadPlateFlag: 1,
  });
  function setDefaultFormData() {
    setFormData2Null(formData);
    formData.appointmentDate = dayjs().format('YYYY-MM-DD');
    formData.cardType = '居民身份证';
    formData.examCategory = '健康体检';
    formData.uploadPlateFlag = 0; // 默认不上报，职业病体检时会自动改为1
    formData.countryCode = '156';
    formData.country = '中国';
  }
  const age = computed(() => {
    //根据出生日期计算年龄
    if (formData.birthday) {
      let birth = new Date(formData.birthday);
      let now = new Date();
      let diff = now.getTime() - birth.getTime();
      return Math.floor(diff / (365.25 * 24 * 60 * 60 * 1000));
    }
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 9 }, md: { span: 9 }, lg: { span: 9 }, xl: { span: 9 }, xxl: { span: 9 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 15 }, md: { span: 15 }, lg: { span: 15 }, xl: { span: 15 }, xxl: { span: 15 } });
  const confirmLoading = ref<boolean>(false);
  const teamList = ref<any[]>([]);
  const companyDeptList = ref<any[]>([]);
  const companyDeptReloadKey = ref<number>(1);
  const companyMatchCheckTimer = ref<any>(null);
  //表单验证
  const validatorRules = reactive({
    examCategory: [{ required: true, message: '请输入体检分类!' }],
    appointmentDate: [{ required: true, message: '请输入预约日期!' }],
    name: [{ required: true, message: '请输入姓名!' }],
    cardType: [{ required: true, message: '请输入证件类型!' }],
    idCard: [
      { required: true, message: '请输入证件号!' },
      {
        validator: (_, value) => {
          if (!value) {
            return Promise.reject('');
          } else {
            if (formData.cardType == '居民身份证' && !IDCardUtils.isValid(value)) {
              return Promise.reject('请输入正确的身份证号!');
            } else {
              return Promise.resolve();
            }
          }
        },
        trigger: 'change',
      },
    ],
    gender: [{ required: true, message: '请输入性别!' }],
    birthday: [{ required: true, message: '请输入出生日期!' }],
    phone: [
      { required: true, message: '请输入电话!' },
      {
        pattern: /^(?:\d{11}|(?:\d{3,4}-)?\d{7,8})$/,
        message: '电话必须是11位数字或固定电话（如：0471-2537660）!',
        trigger: 'blur',
      },
    ],
    career: [{ required: false, message: '请输入职业!' }],
    emergencyContact: [{ required: false, message: '请输入紧急联系人!' }],
    emergencyPhone: [
      { required: false, message: '请输入紧急联系人电话!' },
      { pattern: /^\d{11}$/, message: '电话必须是11位数字!' },
    ],
    jobStatus: [],
    originCustomerIdcard: [
      {
        validator: (_, value) => {
          if (value && value === formData.idCard) {
            return Promise.reject('原体检人不能是本人！');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
    originCustomerRelation: [],
    companyRegId: [],
    teamId: [
      {
        validator: (_, value) => {
          // 如果选择了所属预约，则必须选择所属分组
          if (formData.companyRegId && !value) {
            return Promise.reject('选择所属预约时必须选择所属分组!');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  watch(
    () => formData.riskFactor,
    (newVal) => {
      if (newVal && newVal.length > 0) {
        validatorRules.jobStatus = [{ required: true, message: '请输入岗位类别!' }];
      } else {
        validatorRules.jobStatus = [];
      }
    }
  );
  watch(
    () => formData.originCustomerIdcard,
    (newVal) => {
      if (newVal) {
        validatorRules.originCustomerRelation = [{ required: true, message: '请选择原体检人关系!' }];
      } else {
        validatorRules.originCustomerRelation = [];
      }
    }
  );

  // 监听所属预约变化，重新验证所属分组
  watch(
    () => formData.companyRegId,
    () => {
      // 当所属预约变化时，重新验证所属分组字段
      nextTick(() => {
        if (formRef.value) {
          formRef.value.validateFields(['teamId']).catch(() => {
            // 忽略验证错误，只是为了触发验证显示
          });
        }
      });
    }
  );

  // 监听开始接害日期变化，反向计算接害工龄
  watch(
    () => formData.riskStartTime,
    (newVal) => {
      if (newVal && !isCalculatingRiskTime.value) {
        calculateRiskYearsFromStartTime();
      }
    }
  );
  function handleChangeItemByCompanyTeam(customerRegId, companyTeamId) {
    createConfirm({
      iconType: 'warning',
      title: '变更所属分组同步更换分组下检查项目确认',
      content: '变更所属分组同步更换项目后无法恢复！确认要更换为所选预约分组的项目？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        changeItemGroupByCompanyTeam({ customerRegId: customerRegId, companyTeamId: companyTeamId ? companyTeamId : '' })
          .then((res) => {
            if (res.success) {
              message.success(`项目更换成功`);
              emit('changeItemOk');
            } else {
              message.error(res.message);
              emit('changeItemOk');
            }
          })
          .catch((err) => {
            console.log('同步更换所属项目分组错误', err);
          });
      },
      onCancel: () => {
        message.warn(`取消更换`);
        emit('changeItemCancel');
      },
    });
  }
  watch(
    () => formData.examCategory,
    (newVal) => {
      if (newVal === '干部体检') {
        // 去掉身份证、电话、出生日期、性别的校验
        validatorRules.cardType = [];
        validatorRules.idCard = [];
        validatorRules.phone = [];
        validatorRules.birthday = [];
        //validatorRules.gender = [];
      } else if (newVal === '学生体检') {
        // 去掉身份证、电话、出生日期、性别的校验
        validatorRules.cardType = [];
        validatorRules.idCard = [];
        validatorRules.phone = [];
        //validatorRules.birthday = [];
        //validatorRules.gender = [];
      } else {
        // 恢复原有的校验规则
        validatorRules.idCard = [
          { required: true, message: '请输入证件号!' },
          {
            validator: (_, value) => {
              if (!value) {
                return Promise.reject('');
              } else {
                if (formData.cardType === '居民身份证' && !IDCardUtils.isValid(value)) {
                  return Promise.reject('请输入正确的身份证号!');
                } else {
                  return Promise.resolve();
                }
              }
            },
            trigger: 'change',
          },
        ];
        validatorRules.phone = [
          { required: true, message: '请输入电话!' },
          {
            pattern: /^(?:\d{11}|(?:\d{3,4}-)?\d{7,8})$/,
            message: '电话必须是11位数字或固定电话（如：0471-2537660）!',
          },
        ];
        validatorRules.birthday = [{ required: true, message: '请输入出生日期!' }];
        validatorRules.gender = [{ required: true, message: '请输入性别!' }];
        validatorRules.cardType = [{ required: true, message: '请输入证件类型!' }];
      }
    }
  );
  // 表单禁用
  const disabled = computed(() => {
    return props.formDisabled;
  });

  function setCompany(value) {
    getRootCompanyById({ id: value }).then((res) => {
      formData.companyId = res.id;
      formData.companyName = res.name;
    });
  }

  /**
   * 原检人信息处理
   */
  const originCustomerTeam = ref<any>(null);
  const originCustomerSearchLoading = ref<boolean>(false);
  function handleOriginCustomerSearch(value) {
    if (value) {
      if (value == formData.idCard) {
        createMessage.error('原体检人不能是本人');
        return;
      }

      originCustomerSearchLoading.value = true;
      getTeamAndLimitInfoByIdCard({ idCard: value })
        .then((res) => {
          if (res.success) {
            let result = res.result;
            if (result) {
              formData.originCustomer = result.customerName;
              originCustomerTeam.value = result;
              teamLimitAmount4ShareDetailModal.value?.open(originCustomerTeam.value, formData);
            }
          } else {
            message.error(res.message);
          }
        })
        .finally(() => {
          originCustomerSearchLoading.value = false;
        });
    }
  }

  function handleShareTeamLimitAmount(share) {
    if (share) {
      formData.teamId = null;
      formData.companyRegId = null;
      formData.companyName = null;
      // formData.teamId = originCustomerTeam.value.id;
      // formData.companyRegId = originCustomerTeam.value.companyRegId;
      // formData.companyName = originCustomerTeam.value.companyReg.name;
      formData.originCustomerLimitAmountId = originCustomerTeam.value.customerLimitAmountId;
    } else {
      formData.teamId = null;
      formData.companyRegId = null;
      formData.companyName = null;
      formData.originCustomerLimitAmountId = null;
    }
  }

  function showCompanyTeam() {
    //console.log('showCompanyTeam');
    companyTeamDetailModal.value?.open(formData.companyRegId, formData.teamId);
  }

  const showAddrComp = ref<boolean>(false);
  function handleArea(data) {
    console.log('HealthRecordForm.vue handleArea', data);
    formData.province = data[0]?.name;
    formData.provinceCode = data[0]?.code;
    formData.city = data[1]?.name;
    formData.cityCode = data[1]?.code;
    formData.area = data[2]?.name;
    formData.areaCode = data[2]?.code;
    formData.street = data[3]?.name;
    formData.streetCode = data[3]?.code;
    formData.addressDetail = `${formData.province ?? ''}${formData.city ?? ''}${formData.area ?? ''}${formData.street ?? ''}`;
  }

  function handleCompanyRegChange(companyRegId) {
    if (!companyRegId) {
      teamList.value = [];
      formData.teamId = '';
      return;
    }

    companyTeamList({ companyRegId: companyRegId, pageSize: 10000 }).then((res) => {
      //teamList.value = [];
      //formModel.teamId = '';
      teamList.value = res.records || [];
      if (formData.teamId) {
        let team = teamList.value.find((item) => item.id == formData.teamId);
        if (!team) {
          formData.teamId = null;
        }
      }
    });
    getCompanyRegById({ companyRegId: companyRegId }).then((res) => {
      formData.companyName = res.company?.name;
      formData.companyId = res.companyId;
      formData.industry = res.company?.workIndustry;
      getCompanyDeptList(formData.companyId);
    });
  }

  // CompanyAutoComplete事件处理函数
  const handleCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('选中单位:', value, option);

    // 更新表单数据
    formData.companyId = value;
    formData.companyName = option.name;

    // 自动填充相关信息
    if (option.telephone && !formData.phone) {
      formData.phone = option.telephone;
    }
    if (option.address && !formData.address) {
      formData.address = option.address;
    }

    // 清空部门选择，触发部门树重新加载
    formData.companyDeptId = null;
    formData.companyDeptName = null;
    companyDeptReloadKey.value += 1; // 触发JTreeSelect重新加载
    getCompanyDeptList(value); // 保留原有逻辑以兼容其他地方的使用

    message.success(`已选择单位: ${option.name}`);
  };

  const handleCompanyAutoCompleteChange = (value: string, option?: CompanyAutoCompleteDTO) => {
    console.log('单位变化:', value, option);

    if (!value) {
      // 清空时，清空所有相关字段
      formData.companyId = null;
      formData.companyName = null;
      formData.companyDeptId = null;
      formData.companyDeptName = null;
      companyDeptList.value = [];
      companyDeptReloadKey.value += 1; // 触发部门树重新加载
      message.info('已清空单位选择');
    } else if (!option) {
      // 纯文本输入（未从列表选择），需要检查是否匹配数据库记录
      formData.companyName = value;
      // 延迟检查，避免频繁查询
      checkCompanyMatch(value);
    }
    // 注意：如果option存在，说明是从列表选择的，这种情况由handleCompanySelect处理
  };

  // 部门选择变化处理
  const handleDeptChange = (value: any) => {
    console.log('部门选择变化:', value);
    if (value && typeof value === 'object' && value.value) {
      formData.companyDeptId = value.value;
      formData.companyDeptName = value.label;
    } else if (typeof value === 'string') {
      formData.companyDeptId = value;
      formData.companyDeptName = null; // 如果只有ID，名称由组件自动处理
    } else {
      formData.companyDeptId = null;
      formData.companyDeptName = null;
    }
  };

  /**
   * 处理所属单位变化
   * 获取单位的行业信息并自动填充
   */
  /*  function handleCompanyChange(companyId) {
    if (!companyId) {
      formData.industry = null;
      getCompanyDeptList(companyId);
      return;
    }

    // 获取公司详细信息，包括行业
    getRootCompanyById({ id: companyId })
      .then((res) => {
        if (res && res.workIndustry) {
          formData.industry = res.workIndustry;
        } else {
          formData.industry = '';
        }
      })
      .catch((error) => {
        console.warn('获取公司行业信息失败:', error);
      });

    // 获取部门列表
    getCompanyDeptList(companyId);
  }*/

  function getCompanyDeptList(companyId) {
    if (!companyId) {
      companyDeptList.value = [];
      formData.companyDeptId = '';
      return;
    }

    getCompanyDeptListByPid({ pid: companyId, pageSize: 10000 }).then((res) => {
      companyDeptList.value = res || [];
      if (formData.companyDeptId) {
        let companyDept = companyDeptList.value.find((item) => item.id == formData.companyDeptId);
        if (!companyDept) {
          formData.companyDeptId = null;
        }
      }
    });
  }
  async function handleCameraOk(data) {
    //cameraVisible.value = false;
    //console.log('handleCameraOk', data);

    let uploadRes = await uploadFile(data.blob, 'jpg');
    //console.log('uploadRes', uploadRes);
    formData.customerAvatar = uploadRes.message;
    if (formData.id) {
      saveOrUpdate(formData, true);
    }
  }

  function takePhone() {
    cameraModal.value?.open();
  }

  // 数据来源标识
  const dataSource = ref<'idcard' | 'manual' | 'api' | null>(null);

  /**
   * 统一的身份证数据处理函数
   * 优先级：身份证阅读器 > 手动输入 > 接口数据
   */
  function processIdCardData(idCardNo: string, source: 'idcard' | 'manual' | 'api', additionalData?: any) {
    if (!idCardNo || !IDCardUtils.isValid(idCardNo)) {
      return;
    }

    // 如果当前数据来源优先级更高，则不处理
    if (dataSource.value === 'idcard' && source !== 'idcard') {
      return;
    }
    if (dataSource.value === 'manual' && source === 'api') {
      return;
    }

    // 设置数据来源
    dataSource.value = source;

    // 处理身份证阅读器数据（最高优先级）
    if (source === 'idcard' && additionalData) {
      formData.idCard = idCardNo;
      formData.name = additionalData.idName;
      formData.gender = additionalData.idSex;
      formData.nation = additionalData.idNation;
      formData.birthday = dayjs(additionalData.idBorn, 'YYYYMMDD').format('YYYY-MM-DD');
      formData.customerAvatar = 'data:image/jepg;base64,' + additionalData.idPictureBase64;
      formData.cardType = additionalData.CardTypeName;
      formData.address = additionalData.address;

      // 从身份证号解析年龄
      const idInfo = IDCardUtils.getIDInfo(idCardNo);
      formData.age = idInfo.age;

      // 查询接口数据作为补充（仅补充非核心字段）
      queryCustomerByIdCard({ idCard: idCardNo }).then((res) => {
        if (res.success && res.result) {
          const customer = res.result;
          // 仅补充身份证阅读器没有的字段
          if (!formData.phone && customer.phone) formData.phone = customer.phone;
          if (customer.address) formData.address = customer.address;
          if (customer.career) formData.career = customer.career;
          if (customer.province) {
            formData.province = customer.province;
            formData.city = customer.city;
            formData.area = customer.area;
            formData.provinceCode = customer.provinceCode;
            formData.cityCode = customer.cityCode;
            formData.areaCode = customer.areaCode;
            formData.addressDetail = customer.addressDetail;
          }
          if (customer.countryCode) {
            formData.country = customer.country;
            formData.countryCode = customer.countryCode;
          }
          formData.customerId = customer.id;
          formData.archivesNum = customer.archivesNum;
        }
      });
      return;
    }

    // 处理手动输入数据（中等优先级）
    if (source === 'manual') {
      const idInfo = IDCardUtils.getIDInfo(idCardNo);
      formData.birthday = idInfo.birth;
      formData.gender = idInfo.gender;
      formData.age = idInfo.age;

      // 查询接口数据作为补充
      queryCustomerByIdCard({ idCard: idCardNo }).then((res) => {
        if (res.success && res.result) {
          const customer = res.result;
          // 补充手动输入没有的字段，但不覆盖已解析的核心字段
          if (!formData.name && customer.name) formData.name = customer.name;
          if (!formData.phone && customer.phone) formData.phone = customer.phone;
          if (!formData.career && customer.career) formData.career = customer.career;
          if (!formData.address && customer.address) formData.address = customer.address;
          if (customer.avatar) formData.customerAvatar = customer.avatar;
          if (customer.province) {
            formData.province = customer.province;
            formData.city = customer.city;
            formData.area = customer.area;
            formData.provinceCode = customer.provinceCode;
            formData.cityCode = customer.cityCode;
            formData.areaCode = customer.areaCode;
            formData.addressDetail = customer.addressDetail;
          }
          if (customer.countryCode) {
            formData.country = customer.country;
            formData.countryCode = customer.countryCode;
          }
          formData.customerId = customer.id;
          formData.archivesNum = customer.archivesNum;
        }
      });
      return;
    }

    // 处理接口数据（最低优先级）
    if (source === 'api') {
      queryCustomerByIdCard({ idCard: idCardNo }).then((res) => {
        if (res.success && res.result) {
          const customer = res.result;
          // 只有在字段为空时才填充
          if (!formData.name && customer.name) formData.name = customer.name;
          if (!formData.phone && customer.phone) formData.phone = customer.phone;
          if (!formData.address && customer.address) formData.address = customer.address;
          if (!formData.age && customer.age) formData.age = customer.age;
          if (!formData.gender && customer.gender) formData.gender = customer.gender;
          if (!formData.career && customer.career) formData.career = customer.career;
          if (customer.avatar) formData.customerAvatar = customer.avatar;
          if (customer.province) {
            formData.province = customer.province;
            formData.city = customer.city;
            formData.area = customer.area;
            formData.provinceCode = customer.provinceCode;
            formData.cityCode = customer.cityCode;
            formData.areaCode = customer.areaCode;
            formData.addressDetail = customer.addressDetail;
          }
          if (customer.countryCode) {
            formData.country = customer.country;
            formData.countryCode = customer.countryCode;
          }
          formData.customerId = customer.id;
          formData.archivesNum = customer.archivesNum;
        }
      });
    }
  }

  // Watcher for idCard field (仅在非身份证阅读器来源时触发)
  watch(
    () => formData.idCard,
    (newIdCard) => {
      if (newIdCard && IDCardUtils.isValid(newIdCard) && dataSource.value !== 'idcard') {
        processIdCardData(newIdCard, 'api');
      }
    }
  );
  function handleCountryChange(selectedItems) {
    if (selectedItems && selectedItems.length > 0) {
      formData.country = selectedItems[0].text;
      formData.countryCode = selectedItems[0].value;
    }
  }

  /**
   * 工种选择变化处理
   * 选择工种后自动获取该工种关联的危害因素
   */
  function handleWorkTypeChange(value, option) {
    console.log('工种选择变化:', value, option);

    if (value) {
      // 获取工种关联的危害因素
      getRiskFactorsByWorkType({ workTypeCode: value })
        .then((res) => {
          if (res.success && res.result) {
            // 将获取到的危害因素设置到表单中
            const riskFactors = res.result.map((item) => item.code);
            formData.riskFactor = riskFactors.join(',');
          }
        })
        .catch((error) => {
          console.error('获取工种关联危害因素失败:', error);
          createMessage.warning('获取工种关联危害因素失败，请手动选择');
        });
    } else {
      // 清空危害因素选择
      formData.riskFactor = null;
    }
  }

  /**
   * 岗位类别选择变化处理
   * 选择岗位类别后对已选择的危害因素进行筛选
   */
  function handleJobStatusChange(value, option) {}

  /**
   * 根据接害总工龄计算开始接害日期
   * 计算逻辑：当前日期 - 接害工龄年数 - 接害工龄月数
   */
  function calculateRiskStartTime() {
    if (isCalculatingRiskTime.value) {
      return; // 防止循环计算
    }

    const years = parseInt(formData.riskYears) || 0;
    const months = parseInt(formData.riskMonths) || 0;

    if (years === 0 && months === 0) {
      // 如果工龄为0，清空开始接害日期
      isCalculatingRiskTime.value = true;
      formData.riskStartTime = null;
      setTimeout(() => {
        isCalculatingRiskTime.value = false;
      }, 100);
      return;
    }

    // 设置标志，防止循环计算
    isCalculatingRiskTime.value = true;

    try {
      // 使用当前日期作为基准
      const currentDate = dayjs();

      // 减去年数和月数
      const calculatedDate = currentDate.subtract(years, 'year').subtract(months, 'month');

      // 设置计算出的开始接害日期
      formData.riskStartTime = calculatedDate.format('YYYY-MM-DD');

      // 提示用户计算结果
      createMessage.info(`根据接害工龄自动计算开始接害日期为：${formData.riskStartTime}`);
    } finally {
      // 重置标志
      setTimeout(() => {
        isCalculatingRiskTime.value = false;
      }, 100);
    }
  }

  /**
   * 接害工龄（年）变化处理
   */
  function handleRiskYearsChange() {
    // 延迟执行，确保数据已更新
    nextTick(() => {
      calculateRiskStartTime();
    });
  }

  /**
   * 接害工龄（月）变化处理
   */
  function handleRiskMonthsChange() {
    // 延迟执行，确保数据已更新
    nextTick(() => {
      calculateRiskStartTime();
    });
  }

  /**
   * 根据开始接害日期反向计算接害工龄
   * 计算逻辑：当前日期 - 开始接害日期 = 接害工龄
   */
  function calculateRiskYearsFromStartTime() {
    if (!formData.riskStartTime) {
      return;
    }

    // 设置标志，防止循环计算
    isCalculatingRiskTime.value = true;

    try {
      const startDate = dayjs(formData.riskStartTime);
      const currentDate = dayjs();

      // 计算总月数差
      const totalMonths = currentDate.diff(startDate, 'month');

      if (totalMonths < 0) {
        createMessage.warning('开始接害日期不能晚于当前日期');
        return;
      }

      // 计算年数和月数
      const years = Math.floor(totalMonths / 12);
      const months = totalMonths % 12;

      // 更新表单数据
      formData.riskYears = years.toString();
      formData.riskMonths = months.toString();

      // 提示用户计算结果
      if (years > 0 || months > 0) {
        createMessage.info(`根据开始接害日期自动计算接害工龄为：${years}年${months}个月`);
      }
    } finally {
      // 重置标志
      setTimeout(() => {
        isCalculatingRiskTime.value = false;
      }, 100);
    }
  }

  /**
   * 开始接害日期变化处理
   */
  function handleRiskStartTimeChange() {
    // 延迟执行，确保数据已更新
    nextTick(() => {
      if (!isCalculatingRiskTime.value) {
        calculateRiskYearsFromStartTime();
      }
    });
  }

  /**
   * 开始接害日期输入框失焦处理
   * 验证手动输入的日期格式并进行计算
   */
  function handleRiskStartTimeBlur(event) {
    const inputValue = event.target.value;
    if (!inputValue) {
      return;
    }

    // 验证日期格式 YYYY-MM-DD
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(inputValue)) {
      createMessage.warning('请输入正确的日期格式：YYYY-MM-DD（如：2020-01-15）');
      return;
    }

    // 验证日期是否有效
    const inputDate = dayjs(inputValue, 'YYYY-MM-DD', true);
    if (!inputDate.isValid()) {
      createMessage.warning('请输入有效的日期');
      return;
    }

    // 检查日期是否不晚于当前日期
    const currentDate = dayjs();
    if (inputDate.isAfter(currentDate)) {
      createMessage.warning('开始接害日期不能晚于当前日期');
      return;
    }

    // 检查日期是否过于久远（比如不早于1900年）
    const minDate = dayjs('1900-01-01');
    if (inputDate.isBefore(minDate)) {
      createMessage.warning('开始接害日期不能早于1900年');
      return;
    }

    // 如果验证通过，更新表单数据并触发计算
    formData.riskStartTime = inputValue;
    nextTick(() => {
      if (!isCalculatingRiskTime.value) {
        calculateRiskYearsFromStartTime();
      }
    });
  }

  /**
   * 快速输入接害工龄处理
   * 支持多种格式：'5年3个月'、'5.5年'、'66个月'等
   */
  function handleQuickRiskInputBlur() {
    const input = quickRiskInput.value.trim();
    if (!input) {
      return;
    }

    try {
      let years = 0;
      let months = 0;

      // 匹配 "X年Y个月" 格式
      const yearMonthMatch = input.match(/(\d+(?:\.\d+)?)年(?:(\d+)个?月)?/);
      if (yearMonthMatch) {
        years = parseFloat(yearMonthMatch[1]) || 0;
        months = parseInt(yearMonthMatch[2]) || 0;
      }
      // 匹配 "X.Y年" 格式
      else if (input.match(/^\d+(?:\.\d+)?年?$/)) {
        const yearValue = parseFloat(input.replace('年', ''));
        years = Math.floor(yearValue);
        months = Math.round((yearValue - years) * 12);
      }
      // 匹配 "X个月" 格式
      else if (input.match(/^\d+个?月$/)) {
        const totalMonths = parseInt(input.replace(/个?月/, ''));
        years = Math.floor(totalMonths / 12);
        months = totalMonths % 12;
      }
      // 匹配纯数字，默认为年
      else if (input.match(/^\d+(?:\.\d+)?$/)) {
        const yearValue = parseFloat(input);
        years = Math.floor(yearValue);
        months = Math.round((yearValue - years) * 12);
      } else {
        createMessage.warning('输入格式不正确，请使用如：5年3个月、5.5年、66个月等格式');
        return;
      }

      // 验证范围
      if (years < 0 || years > 100) {
        createMessage.warning('工龄年数应在0-100年之间');
        return;
      }
      if (months < 0 || months > 11) {
        months = months % 12; // 自动转换超过12个月的部分
        years += Math.floor(months / 12);
      }

      // 设置标志，防止循环计算
      isCalculatingRiskTime.value = true;

      // 更新表单数据
      formData.riskYears = years;
      formData.riskMonths = months;

      // 清空快速输入框
      quickRiskInput.value = '';

      // 提示用户解析结果
      createMessage.success(`已解析为：${years}年${months}个月`);

      // 延迟计算开始日期
      setTimeout(() => {
        isCalculatingRiskTime.value = false;
        calculateRiskStartTime();
      }, 100);
    } catch (error) {
      createMessage.error('解析输入失败，请检查输入格式');
    }
  }

  // 字段显示位置计算逻辑

  /**
   * 获取所有可能的字段列表（包括模板中使用但未配置的字段）
   * 向后兼容：确保所有实际使用的字段都有对应的配置
   */
  const getAllPossibleFields = computed(() => {
    // 从备份文件中提取所有实际使用的字段，按照原有的显示逻辑
    const templateFieldsWithNames = [
      // 核心字段 - 原本就在外部区域显示
      { key: 'examCategory', name: '体检分类' },
      { key: 'appointmentDate', name: '预约日期' },
      { key: 'name', name: '姓名' },
      { key: 'cardType', name: '证件类型' },
      { key: 'idCard', name: '证件号' },
      { key: 'gender', name: '性别' },
      { key: 'age', name: '年龄' },
      { key: 'birthday', name: '出生日期' },
      { key: 'phone', name: '电话' },
      { key: 'career', name: '职业' },

      // 条件显示字段 - 原本通过 !isFieldInCollapse() 控制
      { key: 'pcaCode', name: '省市区县' },
      { key: 'address', name: '详细地址' },
      { key: 'emergencyContact', name: '紧急联系人' },
      { key: 'emergencyPhone', name: '紧急电话' },

      // 折叠区域字段 - 原本在折叠面板中
      { key: 'nation', name: '民族' },
      { key: 'bloodType', name: '血型' },
      { key: 'countryCode', name: '国籍' },
      { key: 'postCode', name: '邮政编码' },
      { key: 'eduLevel', name: '文化程度' },
      { key: 'marriageStatus', name: '婚姻状况' },
      { key: 'customerCategory', name: '客户类别' },
      { key: 'isPregnancyPrep', name: '是否备孕' },
      { key: 'healthNo', name: '健康证号' },
      { key: 'examCardNo', name: '体检卡号' },
      { key: 'workYears', name: '工龄' },
      { key: 'companyName', name: '单位名称' },
      { key: 'belongCompany', name: '所属单位' },
      { key: 'department', name: '所属部门' },
      { key: 'supplyFlag', name: '补检' },
      { key: 'prePayFlag', name: '预缴' },
      { key: 'reExamStatus', name: '是否复查' },
      { key: 'reExamRemark', name: '复查备注' },
      { key: 'recipeTitle', name: '发票抬头' },
      { key: 'originalIdCard', name: '原检证件号' },
      { key: 'relationWithOriginal', name: '与原检关系' },
      { key: 'introducer', name: '介绍人' },
      { key: 'secretLevel', name: '保密等级' },
      { key: 'remark', name: '备注' },
    ];

    const configuredFields = fieldDisplayConfig.value;
    const allFields = [...configuredFields];

    // 为没有配置的字段创建默认配置（向后兼容）
    templateFieldsWithNames.forEach(({ key: fieldKey, name: fieldName }) => {
      if (!configuredFields.find((f) => f.fieldKey === fieldKey)) {
        allFields.push({
          fieldKey,
          fieldName, // 使用中文名称
          isVisible: true,
          displayLocation: FieldDisplayLocation.OUTSIDE, // 向后兼容：默认在外部区域
          sortOrder: 999, // 默认排序在最后
        });
      }
    });

    return allFields;
  });

  /**
   * 外部区域字段（基于配置动态计算）
   * 直接从配置中筛选，避免依赖getAllPossibleFields
   */
  const outsideFields = computed(() => {
    return fieldDisplayConfig.value
      .filter((field) => {
        // 字段必须可见
        if (!field.isVisible) {
          return false;
        }
        // 字段显示位置必须是外部区域
        return field.displayLocation === FieldDisplayLocation.OUTSIDE;
      })
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
  });

  /**
   * 折叠区域字段（基于配置动态计算）
   * 直接从配置中筛选，避免依赖getAllPossibleFields
   */
  const collapseFields = computed(() => {
    return fieldDisplayConfig.value
      .filter((field) => {
        // 字段必须可见
        if (!field.isVisible) {
          return false;
        }
        // 字段显示位置必须是折叠区域
        return field.displayLocation === FieldDisplayLocation.COLLAPSE;
      })
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
  });

  /**
   * 隐藏字段（基于配置动态计算）
   * 直接从配置中筛选，避免依赖getAllPossibleFields
   */
  const hiddenFields = computed(() => {
    return fieldDisplayConfig.value
      .filter((field) => {
        // 字段不可见或显示位置为隐藏
        return !field.isVisible || field.displayLocation === FieldDisplayLocation.HIDDEN;
      })
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
  });

  /**
   * 可见字段（向后兼容）
   */
  const visibleFields = computed(() => {
    return collapseFields.value; // 折叠区域的可见字段
  });

  /**
   * 加载字段显示配置
   */
  async function loadFieldDisplayConfig() {
    try {
      // 这里应该从用户信息或系统配置中获取当前体检中心ID
      const centerId = 'current_center_id'; // 实际应该从登录用户信息获取

      const config = await getActiveFormDisplayConfig({
        centerId,
        formType: FORM_TYPES.CUSTOMER_REG,
      });

      if (config && config.fields) {
        // 迁移和验证配置
        let migratedFields = migrateFieldConfig(config.fields);
        const validation = validateFieldConfig(migratedFields);

        if (validation.warnings.length > 0) {
          console.warn('字段配置警告:', validation.warnings);
        }

        if (!validation.isValid) {
          console.error('字段配置验证失败:', validation.errors);
          throw new Error('字段配置无效: ' + validation.errors.join(', '));
        }

        fieldDisplayConfig.value = migratedFields;
        console.log('加载字段显示配置成功:', fieldDisplayConfig.value);
      } else {
        // 如果没有配置，使用默认配置
        fieldDisplayConfig.value = getDefaultFieldConfig();
        console.log('使用默认字段配置');
      }
    } catch (error) {
      console.warn('加载字段显示配置失败，尝试从本地存储加载:', error);

      // 如果是 404 错误，说明后端接口还没有实现，尝试从本地存储读取
      if (error?.response?.status === 404) {
        console.info('后端接口尚未实现，尝试从本地存储读取配置');
        try {
          const localConfig = localStorage.getItem('fieldDisplayConfig');
          if (localConfig) {
            const parsedConfig = JSON.parse(localConfig);
            if (parsedConfig.fields && Array.isArray(parsedConfig.fields)) {
              // 迁移和验证本地配置
              let migratedFields = migrateFieldConfig(parsedConfig.fields);
              const validation = validateFieldConfig(migratedFields);

              if (validation.isValid) {
                fieldDisplayConfig.value = migratedFields;
                console.log('从本地存储加载配置成功');
                return;
              } else {
                console.warn('本地配置验证失败，使用默认配置:', validation.errors);
              }
            }
          }
        } catch (localError) {
          console.warn('从本地存储读取配置失败:', localError);
        }
      }

      // 使用预设配置作为最终后备
      fieldDisplayConfig.value = getDefaultFieldConfig();
      console.log('使用默认字段配置');
    }
  }

  /**
   * 处理地址变化
   */
  function handleAreaChange(value: string, selectedOptions: any[]) {
    if (selectedOptions && selectedOptions.length > 0) {
      formData.province = selectedOptions[0]?.label || '';
      formData.city = selectedOptions[1]?.label || '';
      formData.area = selectedOptions[2]?.label || '';
      formData.pcaCode = value;
    }
  }

  // 权限检查 - 检查用户是否有配置权限
  const hasConfigPermission = computed(() => {
    // 这里应该根据实际的权限系统来判断
    // 示例：检查用户是否有管理员权限
    return true; // 实际应该从用户权限中获取，例如：hasPermission('field:config')
  });

  /**
   * 字段显示位置检查逻辑（基于配置的动态判断）
   *
   * 新的架构设计：
   * 1. 统一的配置源：只维护 fieldDisplayConfig 一个配置数组
   * 2. 动态计算：所有字段列表（outsideFields、collapseFields、hiddenFields）都基于配置动态计算
   * 3. 统一判断：提供 isFieldInLocation 作为统一的位置判断方法
   * 4. 避免硬编码：不再需要在多个地方维护字段列表
   * 5. 向后兼容：保留原有的方法，但建议逐步迁移到新方法
   */

  /**
   * 获取字段的配置信息
   */
  function getFieldConfig(fieldKey: string): FieldDisplayConfig | null {
    return fieldDisplayConfig.value.find((f) => f.fieldKey === fieldKey) || null;
  }

  /**
   * 检查字段是否在指定位置显示
   * 统一的判断逻辑，避免硬编码
   */
  function isFieldInLocation(fieldKey: string, location: FieldDisplayLocation): boolean {
    const fieldConfig = getFieldConfig(fieldKey);

    // 向后兼容：如果字段没有配置，默认在外部区域显示
    if (!fieldConfig) {
      return location === FieldDisplayLocation.OUTSIDE;
    }

    // 如果字段不可见，则不在任何位置显示
    if (!fieldConfig.isVisible) {
      return false;
    }

    // 检查字段是否在指定位置
    return fieldConfig.displayLocation === location;
  }

  /**
   * 检查字段是否应该在外部区域显示
   * 基于配置的动态判断，不依赖硬编码的字段列表
   */
  function shouldShowInOutside(fieldKey: string): boolean {
    return isFieldInLocation(fieldKey, FieldDisplayLocation.OUTSIDE);
  }

  /**
   * 检查字段是否应该在折叠区域显示
   */
  function shouldShowInCollapse(fieldKey: string): boolean {
    return isFieldInLocation(fieldKey, FieldDisplayLocation.COLLAPSE);
  }

  /**
   * 检查字段是否被隐藏
   */
  function shouldHideField(fieldKey: string): boolean {
    const fieldConfig = getFieldConfig(fieldKey);

    // 向后兼容：如果字段没有配置，默认不隐藏
    if (!fieldConfig) {
      return false;
    }

    return !fieldConfig.isVisible || fieldConfig.displayLocation === FieldDisplayLocation.HIDDEN;
  }

  /**
   * 检查字段是否应该在指定位置显示
   * 向后兼容：没有配置的字段默认在外部区域显示
   */
  function shouldShowFieldAt(fieldKey: string, location: FieldDisplayLocation): boolean {
    const field = getAllPossibleFields.value.find((f) => f.fieldKey === fieldKey);

    // 向后兼容：如果字段没有配置，默认在外部区域显示
    if (!field) {
      return location === FieldDisplayLocation.OUTSIDE;
    }

    // 如果字段不可见，则不显示
    if (!field.isVisible) return false;

    // 优先级：outside > collapse > hidden
    return field.displayLocation === location;
  }

  /**
   * 检查字段是否在外部区域显示
   */
  function isFieldInOutside(fieldKey: string): boolean {
    return shouldShowFieldAt(fieldKey, FieldDisplayLocation.OUTSIDE);
  }

  /**
   * 检查字段是否在折叠面板中配置显示（向后兼容）
   */
  function isFieldInCollapse(fieldKey: string): boolean {
    return shouldShowFieldAt(fieldKey, FieldDisplayLocation.COLLAPSE);
  }

  /**
   * 检查字段是否被隐藏
   * 向后兼容：没有配置的字段默认不隐藏
   */
  function isFieldHidden(fieldKey: string): boolean {
    const field = fieldDisplayConfig.value.find((f) => f.fieldKey === fieldKey);

    // 向后兼容：没有配置的字段默认不隐藏（显示在外部区域）
    if (!field) return false;

    return !field.isVisible || field.displayLocation === FieldDisplayLocation.HIDDEN;
  }

  /**
   * 获取字段的显示位置
   * 向后兼容：没有配置的字段默认在外部区域
   */
  function getFieldDisplayLocation(fieldKey: string): FieldDisplayLocation {
    const field = fieldDisplayConfig.value.find((f) => f.fieldKey === fieldKey);

    // 向后兼容：没有配置的字段默认在外部区域显示
    return field?.displayLocation || FieldDisplayLocation.OUTSIDE;
  }

  /**
   * 通用字段渲染函数
   * 根据字段类型返回对应的组件配置
   */
  function getFieldComponent(fieldKey: string) {
    const fieldComponents = {
      // 基础输入字段
      name: () => ({
        component: 'a-input',
        props: { placeholder: '请输入姓名' },
      }),
      age: () => ({
        component: 'a-input',
        props: { placeholder: '请输入年龄' },
      }),
      nation: () => ({
        component: 'a-input',
        props: { placeholder: '请输入民族' },
      }),
      postCode: () => ({
        component: 'a-input',
        props: { placeholder: '请输入邮政编码' },
      }),
      healthNo: () => ({
        component: 'a-input',
        props: { placeholder: '请输入健康证号' },
      }),
      examCardNo: () => ({
        component: 'a-input',
        props: { placeholder: '请输入体检卡号' },
      }),
      companyName: () => ({
        component: 'a-input',
        props: { placeholder: '请输入单位名称' },
      }),
      belongCompany: () => ({
        component: 'a-input',
        props: { placeholder: '请输入所属单位' },
      }),
      department: () => ({
        component: 'a-input',
        props: { placeholder: '请输入所属部门' },
      }),
      reExamRemark: () => ({
        component: 'a-input',
        props: { placeholder: '请输入复查备注' },
      }),
      recipeTitle: () => ({
        component: 'a-input',
        props: { placeholder: '请输入发票抬头' },
      }),
      originalIdCard: () => ({
        component: 'a-input',
        props: { placeholder: '请输入原检证件号' },
      }),
      introducer: () => ({
        component: 'a-input',
        props: { placeholder: '请输入介绍人' },
      }),
      emergencyContact: () => ({
        component: 'a-input',
        props: { placeholder: '请输入紧急联系人' },
      }),
      emergencyPhone: () => ({
        component: 'a-input',
        props: { placeholder: '请输入紧急电话' },
      }),

      // 数字输入字段
      workYears: () => ({
        component: 'a-input-number',
        props: { placeholder: '请输入工龄', style: 'width: 100%' },
      }),

      // 文本域字段
      address: () => ({
        component: 'a-textarea',
        props: { placeholder: '请输入详细地址', rows: 2 },
      }),
      remark: () => ({
        component: 'a-textarea',
        props: { placeholder: '请输入备注', rows: 3 },
      }),

      // 字典选择字段
      bloodType: () => ({
        component: 'j-dict-select-tag',
        props: {
          dictCode: 'blood_type',
          placeholder: '请选择血型',
          useDicColor: true,
        },
      }),
      eduLevel: () => ({
        component: 'j-dict-select-tag',
        props: {
          dictCode: 'edu_level',
          placeholder: '请选择文化程度',
        },
      }),
      marriageStatus: () => ({
        component: 'j-dict-select-tag',
        props: {
          dictCode: 'marriage_status',
          placeholder: '请选择婚姻状况',
        },
      }),
      customerCategory: () => ({
        component: 'j-dict-select-tag',
        props: {
          dictCode: 'customer_category',
          placeholder: '请选择客户类别',
        },
      }),
      relationWithOriginal: () => ({
        component: 'j-dict-select-tag',
        props: {
          dictCode: 'relation_type',
          placeholder: '请选择与原检关系',
        },
      }),
      secretLevel: () => ({
        component: 'j-dict-select-tag',
        props: {
          dictCode: 'secret_level',
          placeholder: '请选择保密等级',
          useDicColor: true,
        },
      }),

      // 异步搜索选择字段
      countryCode: () => ({
        component: 'j-async-search-select',
        props: {
          dict: 'country',
          placeholder: '请选择国籍',
        },
      }),

      // 开关字段
      supplyFlag: () => ({
        component: 'j-switch',
        props: { options: [1, 0] },
      }),
      prePayFlag: () => ({
        component: 'j-switch',
        props: { options: [1, 0] },
      }),
      reExamStatus: () => ({
        component: 'j-switch',
        props: { options: [1, 0] },
      }),
      isPregnancyPrep: () => ({
        component: 'j-switch',
        props: { options: [1, 0] },
      }),

      // 特殊字段
      pcaCode: () => ({
        component: 'j-area-linkage',
        props: {
          type: 'cascader',
          placeholder: '请选择省市区县',
        },
        span: 24,
      }),
    };

    return fieldComponents[fieldKey]
      ? fieldComponents[fieldKey]()
      : {
          component: 'a-input',
          props: { placeholder: `请输入${fieldKey}` },
        };
  }

  /**
   * 打开字段配置弹窗
   */
  function openFieldConfig() {
    configModalRef.value?.open();
  }

  /**
   * 配置成功回调
   */
  function handleConfigSuccess() {
    // 重新加载配置
    loadFieldDisplayConfig();
  }

  /**
   * 新增
   */
  function add() {
    setDefaultFormData();
    // 重置数据来源标识
    dataSource.value = null;
    // 加载字段显示配置
    loadFieldDisplayConfig();

    // 开发环境下测试向后兼容性
    if (import.meta.env.DEV) {
      console.log('=== 字段显示位置测试 ===');
      setTimeout(() => {
        console.log('外部区域字段数量:', outsideFields.value.length);
        console.log('折叠区域字段数量:', collapseFields.value.length);
        console.log('隐藏字段数量:', hiddenFields.value.length);
        testBackwardCompatibility();
      }, 100);
    }

    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    showAddrComp.value = false;
    // 重置数据来源标识
    dataSource.value = null;
    // 加载字段显示配置
    loadFieldDisplayConfig();
    nextTick(() => {
      resetFields();
      //赋值
      Object.assign(formData, record);
      originCompanyTeamId.value = formData.teamId;
      if (!formData.countryCode) {
        formData.countryCode = '156'; // 默认值为中国的 code
        formData.country = '中国'; // 默认值为中国的 label
      }
      if (formData.companyRegId) {
        handleCompanyRegChange(formData.companyRegId);
      }
    });
  }

  function handleIdCard(e) {
    let value = e.target.value;
    if (value && IDCardUtils.isValid(value)) {
      // 使用统一的数据处理函数，标记为手动输入
      processIdCardData(value, 'manual');
    }
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    try {
      await validate();
    } catch (e) {
      const errorMessages = e.errorFields
        .map((field) => {
          return `${field.errors.join('')}`;
        })
        .join('<br/>');

      createErrorModal({
        title: '表单验证失败',
        content: `${errorMessages}`,
      });

      //console.log(e);
      //给出更详细的提示，能够提示出哪个字段的错误
      //message.error('请检查表单是否填写完整！');
      return;
    }

    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = toRaw(formData);
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    model.age = age.value;
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          const flag = originCompanyTeamId.value == res.result.teamId;
          Object.assign(formData, res.result);
          createMessage.success(res.message);
          if (originCompanyTeamId.value && !flag && formData.teamId) {
            getItemGroupOfTeam({ teamId: formData.teamId }).then((res) => {
              if (res.success && res.result.length) {
                handleChangeItemByCompanyTeam(formData.id, formData.teamId);
              }
            });
          }
          emit('ok', res.result);
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  function reg() {
    let model = formData;
    if (!model.id) {
      message.error('请先保存基本信息！');
      return;
    }
    if (model.status != '未登记') {
      message.error('不能重复登记！');
      return;
    }

    confirmLoading.value = true;
    regOne({ id: model.id })
      .then((res) => {
        if (res.success) {
          message.success(res.message);
          emit('regOk', res.result);
        } else {
          message.error(res.message);
        }
      })
      .finally((e) => {
        confirmLoading.value = false;
      });
  }

  watchEffect(() => {
    if (props.formData) {
      Object.assign(formData, props.formData);
    }
  });

  const idcDataSource = computed(() => ({
    data: idcData.data,
    action: idcData.action,
  }));

  watch(idcDataSource, (val) => {
    if (val.data.idCardNo && val.action == 'addReg') {
      if (val.data.idCardNo != formData.idCard) {
        setDefaultFormData();
      }
      // 使用统一的数据处理函数，标记为身份证阅读器来源
      processIdCardData(val.data.idCardNo, 'idcard', val.data);
    }
  });

  defineExpose({
    add,
    edit,
    submitForm,
    reg,
    // 字段显示位置相关方法和属性（基于配置的动态判断）
    outsideFields,
    collapseFields,
    hiddenFields,
    // 新的统一判断方法
    shouldShowInOutside,
    shouldShowInCollapse,
    shouldHideField,
    isFieldInLocation,
    getFieldConfig,
    // 向后兼容的方法（建议逐步迁移到新方法）
    isFieldInOutside,
    isFieldInCollapse,
    isFieldHidden,
    getFieldDisplayLocation,
    loadFieldDisplayConfig,
  });
</script>

<style scoped>
  .customer-header-info {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
  }

  .status-bar {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    gap: 16px;
    background: #fff;
  }

  .avatar-section {
    flex-shrink: 0;
  }

  .avatar-container {
    position: relative;
    display: inline-block;
  }

  .customer-avatar {
    border: 2px solid #e8e8e8;
    border-radius: 6px;
    background: #f5f5f5;
    overflow: hidden;
  }

  .customer-avatar :deep(.ant-image-img) {
    object-fit: cover;
    border-radius: 4px;
  }

  .photo-btn {
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    height: 24px;
    width: 24px;
    padding: 0;
    color: #666;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .photo-btn:hover {
    color: #1890ff;
    border-color: #1890ff;
  }

  .status-tags {
    flex: 1;
    min-width: 0;
  }

  .status-row {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 12px 16px;
  }

  .status-group {
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 0;
  }

  .status-label {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    font-weight: 500;
  }

  .status-tag {
    font-size: 12px;
    margin: 0;
    border-radius: 4px;
    font-weight: 500;
  }

  .status-tag-small {
    font-size: 11px;
    margin: 0 0 0 4px;
    padding: 0 4px;
    border-radius: 3px;
  }

  .detail-toggle-inline {
    flex-shrink: 0;
    display: flex;
    align-items: center;
  }

  .detail-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    color: #666;
    text-decoration: none;
    transition: all 0.2s;
    font-size: 12px;
  }

  .detail-toggle-btn:hover {
    background: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
  }

  .detail-info {
    border-top: 1px solid #e8e8e8;
    padding: 12px;
    background: #fff;
  }

  .detail-link {
    margin-left: 8px;
    font-size: 12px;
    color: #1890ff;
    text-decoration: none;
  }

  .detail-link:hover {
    color: #40a9ff;
  }

  /* 响应式调整 */
  @media (max-width: 1200px) {
    .status-row {
      gap: 8px 12px;
    }

    .status-group {
      min-width: auto;
    }

    .detail-info {
      padding: 8px;
    }
  }

  @media (max-width: 768px) {
    .status-bar {
      flex-direction: column;
      align-items: center;
      gap: 12px;
      padding: 12px;
    }

    .status-row {
      justify-content: center;
      gap: 8px;
      flex: 1;
    }

    .status-group {
      flex-direction: column;
      align-items: center;
      gap: 4px;
      text-align: center;
    }

    .detail-toggle-inline {
      margin-top: 8px;
    }

    .detail-info {
      padding: 8px;
    }
  }
</style>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 5px;
  }
  .row-title {
    position: relative;
    justify-content: space-between;
    display: flex;
    align-items: center;
    padding: 5px 20px;
  }

  .row-title:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 3px; /* 边框宽度 */
    height: 16px; /* 边框高度 */
    background-color: v-bind('token.colorPrimary'); /* 边框颜色 */
    transform: translateY(-50%);
  }

  /* Target all a-form-item components within this component */
  :deep(.ant-form-item) {
    margin-bottom: 4px; /* Reduce bottom margin */
    margin-top: 4px; /* Reduce top margin */
    padding: 0; /* Remove padding */
  }

  /* Optional: Adjust the label padding */
  :deep(.ant-form-item-label) {
    padding: 0;
    line-height: 1;
  }

  /* 字段配置相关样式 */
  .no-fields-tip {
    padding: 40px 0;
    text-align: center;
    background: #fafafa;
    border-radius: 6px;
    margin: 16px 0;
  }

  .row-title .ant-space {
    align-items: center;
  }

  .row-title .ant-btn[disabled] {
    opacity: 0.6;
  }
</style>
