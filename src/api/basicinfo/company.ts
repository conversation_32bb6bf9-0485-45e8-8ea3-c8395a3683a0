import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/basicinfo/company/list',
  queryById = '/basicinfo/company/queryById',
  search = '/basicinfo/company/search',
  save = '/basicinfo/company/add',
  edit = '/basicinfo/company/edit',
  deleteOne = '/basicinfo/company/delete',
  deleteBatch = '/basicinfo/company/deleteBatch',
  importExcel = '/basicinfo/company/importExcel',
  exportXls = '/basicinfo/company/exportXls',
  autoComplete = '/basicinfo/company/autoComplete',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => {
  return defHttp.get({ url: Api.list, params });
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 导入
 * @param params
 */
export const importExcel = (params) => {
  return defHttp.uploadFile({ url: Api.importExcel }, params);
};

/**
 * 导出
 * @param params
 */
export const exportXls = (params) => {
  return defHttp.get({ url: Api.exportXls, params }, { responseType: 'blob' });
};

/**
 * 根据ID查询
 * @param id
 */
export const queryById = (id) => {
  return defHttp.get({ url: Api.queryById, params: { id } });
};

/**
 * 搜索公司
 * @param keyword
 * @param onlyRootCompanies
 */
export const searchCompanyByKeyword = (keyword: string, onlyRootCompanies: boolean = true) => {
  const params: any = {
    pageNo: 1,
    pageSize: 50,
  };

  if (keyword && keyword.trim()) {
    params.name_LIKE = keyword.trim();
    params.helpChar_LIKE = keyword.trim();
    params._searchMode = 'OR';
  }

  if (onlyRootCompanies) {
    params.pid_NULL = true;
  }

  return defHttp.get({ url: Api.list, params });
};

/**
 * 单位自动完成接口（带缓存）
 * @param keyword 搜索关键词
 * @param pageSize 返回数量限制，默认50
 */
export const getCompanyAutoComplete = (keyword?: string, pageSize: number = 50) => {
  const params: any = {
    pageSize,
  };

  if (keyword && keyword.trim()) {
    params.keyword = keyword.trim();
  }

  return defHttp.get({ url: Api.autoComplete, params });
};
